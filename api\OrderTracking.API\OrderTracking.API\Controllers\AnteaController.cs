﻿using AIMaaS.Models;
using AIMaaS.Services;
using ClientPortal.Shared.Extensions;
using ClientPortal.Shared.Models;
using ClientPortal.Shared.Services;
// Migrated from Google Cloud to Azure
// using Google.Cloud.Firestore;
// using Google.Cloud.Storage.V1;
using Microsoft.Azure.Cosmos;
using Azure.Storage.Blobs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OrderTracking.API.Models;
using OrderTracking.API.Services;
using OrderTracking.API.Interfaces.Services;
using OrderTracking.API.Extensions;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using Microsoft.Extensions.Logging;

namespace OrderTracking.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class AnteaController : ControllerBase
    {
        private readonly IAnteaService _anteaService;
        private readonly String _anteaAttachmentsBucketName;
        private readonly IUserProfilesService _userProfiles;
        // Migrated from Firestore to Azure Cosmos DB
        private readonly Container _cosmosContainer;
        private readonly Container _submissionsContainer;
        private readonly CosmosClient _cosmosClient;
        private readonly IEmailService _emails;
        private readonly ILogger<AnteaController> _logger;
        private readonly ICloudStorageService _cloudStorageService;

        public AnteaController(IAnteaService anteaService, IUserProfilesService userProfiles, IOptions<Connections> options,
            IEmailService emails, ICosmosClientAdapter cosmosClientAdapter, ICloudStorageService cloudStorageService, ILogger<AnteaController> logger)
        {
            _anteaService = anteaService;
            _userProfiles = userProfiles;
            _emails = emails;
            _cloudStorageService = cloudStorageService;
            _logger = logger;

            // Migrated from Firestore to Azure Cosmos DB
            _cosmosClient = cosmosClientAdapter.GetClient();
            var database = _cosmosClient.GetDatabase(options.Value.Database ?? "OrderTrackingDB");
            _cosmosContainer = database.GetContainer(options.Value.UserProfiles ?? "UserProfiles");
            _submissionsContainer = database.GetContainer(options.Value.OISSubmissions ?? "OIS-Submissions");

            _anteaAttachmentsBucketName = options.Value.AnteaAttachmentsBlobContainer;


        }



        [HttpGet("DownloadFile")]
        public async Task<IActionResult> DownloadFile([FromQuery] string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
            {
                return BadRequest("File path is required.");
            }

            var objectName = HttpUtility.UrlDecode(filePath);
            try
            {
                // Migrated from Google Cloud Storage to Azure Blob Storage
                var downloadedObject = await _cloudStorageService.DownloadObjectAsync(_anteaAttachmentsBucketName, objectName);

                // Convert stream to byte array
                byte[] bytes;
                using (var memoryStream = new MemoryStream())
                {
                    downloadedObject.Stream.CopyTo(memoryStream);
                    bytes = memoryStream.ToArray();
                }

                string base64 = Convert.ToBase64String(bytes);
                return Ok(new { data = base64, filetype = GetContentTypeFromExtension(Path.GetExtension(objectName)) });
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = "File is temporarily unavailable. Please contact your TEAM AIOM support for expedited access.\n\nDetails: " + ex.Message.ToString() });

            }
        }

        private string GetContentTypeFromExtension(string extension)
        {
            extension = extension.ToLower();

            if (extension.Contains(".pdf"))
            {
                return "application/pdf";
            }
            else if (extension.Contains(".docx"))
            {
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            }
            else if (extension.Contains(".xlsx"))
            {
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            }
            else if (extension.Contains(".png"))
            {
                return "image/png";
            }
            else if (extension.Contains(".jpg") || extension.Contains(".jpeg"))
            {
                return "image/jpeg";
            }
            else if (extension.Contains(".gif"))
            {
                return "image/gif";
            }
            else
            {
                return "application/octet-stream";
            }
        }


        [HttpPost("addPreferenceField")]
        public async Task<IActionResult> AddField([FromBody] Dictionary<string, string> requestData)
        {
            string userId = requestData.ContainsKey("userId") ? requestData["userId"] : null;
            string storageKey = requestData.ContainsKey("storageKey") ? requestData["storageKey"] : null;
            string value = requestData.ContainsKey("value") ? requestData["value"] : null;
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(storageKey))
            {
                return BadRequest("userId, storageKey, and value are required.");
            }

            try
            {
                // Migrated from Firestore to Azure Cosmos DB
                try
                {
                    var response = await _cosmosContainer.ReadItemAsync<dynamic>(userId, new PartitionKey(userId));
                    var existingDoc = response.Resource;

                    // Convert to JObject for proper handling
                    var docJObject = JObject.FromObject(existingDoc);

                    // Get or create preference object
                    var preferences = docJObject["preference"] as JObject ?? new JObject();
                    preferences[storageKey] = value;
                    docJObject["preference"] = preferences;

                    await _cosmosContainer.UpsertItemAsync(docJObject, new PartitionKey(userId));
                    return Ok("Preference updated successfully.");
                }
                catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound("User document not found.");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpGet("getPreferenceField")]
        public async Task<IActionResult> GetField([FromQuery] string userId, [FromQuery] string storageKey)
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(storageKey))
            {
                return BadRequest("userId and storageKey are required.");
            }

            try
            {
                // Migrated from Firestore to Azure Cosmos DB
                try
                {
                    var response = await _cosmosContainer.ReadItemAsync<dynamic>(userId, new PartitionKey(userId));
                    var document = response.Resource;

                    if (document.preference != null)
                    {
                        return Ok(document.preference);
                    }
                    return Ok();
                }
                catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound("User document not found.");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }
        private async Task<IEnumerable<T>> FilterByUserRoleAsync<T>(IEnumerable<T> items, Func<T, string> getLocationId)
        {
            var email = User?.Identity?.Name?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            var hasAdminRole = user.HasRole("aimaas:admin") || user.HasRole("app:admin");
            var hasDemoRole = user.HasRole("aimaas:demo");
            var isDistrictUser = user.HasRole("aimaas:district");
            var isClientUser = user.HasRole("aimaas:client");
            var isAimaasallUser = user.HasRole("aimaas:all");

            if (hasAdminRole) return items;

            if (hasDemoRole)
            {
                var sites = await _anteaService.GetAllAssetManagementSitesAsync();
                var demoSite = sites.FirstOrDefault(site => site.LOCATIONNAME.Trim().ToUpper().Contains("DEMO SITE"));
                return demoSite != null ? items.Where(item => getLocationId(item) == demoSite.LOCATIONID) : Enumerable.Empty<T>();
            }

            if (!(isDistrictUser || isClientUser || isAimaasallUser))
            {
                return items.Where(item => user.AssetManagementSiteIds.Contains(getLocationId(item)));
            }

            var districtIds = isDistrictUser || isAimaasallUser
                ? user.DistrictIds.Select(d => d.Split(' ')[0]).ToList()  // Extract first part
                : null;
            var customerAccounts = isClientUser || isAimaasallUser ? user.CustomerAccounts : null;

            var filteredData = await _anteaService.GetClientLocationDataByIDAsync(districtIds, customerAccounts, user.Roles);
            var locationIds = new HashSet<string>(filteredData.Select(d => d.LOCATIONID));

            return items.Where(item => locationIds.Contains(getLocationId(item)));
        }

        [HttpGet("Assests")]
        public async Task<IActionResult> GetAssets()
        {
            try
            {
                var assets = await _anteaService.GetAssetsAsync();
                var filteredAssets = await FilterByUserRoleAsync(assets, a => a.LOCATIONID);
                return Ok(filteredAssets);
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("AssetManagementSites")]
        public async Task<IActionResult> GetAllAssetManagementSites()
        {
            try
            {
                var sites = await _anteaService.GetAllAssetManagementSitesAsync();
                var filteredSites = await FilterByUserRoleAsync(sites, s => s.LOCATIONID);
                return Ok(filteredSites);
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("AssetAttachments")]
        public async Task<IActionResult> GetAssetAttachments(string assetid)
        {
            try
            {
                var assetAttachements = await _anteaService.GetAssetAttachmentsAsync(assetid);
                return Ok(assetAttachements);
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("AssetComponents")]
        public async Task<IActionResult> GetAssetComponents(string assetId)
        {
            try
            {
                return Ok(await _anteaService.GetAssetComponentsAsync(assetId));
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("ChamberData")]
        public async Task<IActionResult> GetChamberData(string assetId)
        {
            try
            {
                return Ok(await _anteaService.GetChamberDataAsync(assetId));
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("ClientLocationData")]
        public async Task<IActionResult> GetClientLocationData()
        {
            var email = User?.Identity?.Name?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            var hasAdminRole = user.HasRole("aimaas:admin") || user.HasRole("app:admin");
            var isDistrictUser = user.HasRole("aimaas:district");
            var isClientUser = user.HasRole("aimaas:client");
            var isAimaasallUser = user.HasRole("aimaas:all");
            try
            {
                if (!hasAdminRole && isDistrictUser)
                {
                    return Ok(await _anteaService.GetClientLocationDataByIDAsync(user.DistrictIds, null, user.Roles));
                }

                if (!hasAdminRole && isClientUser)
                    return Ok(await _anteaService.GetClientLocationDataByIDAsync(null, user.CustomerAccounts, user.Roles));
                if (!hasAdminRole && isAimaasallUser)
                    return Ok(await _anteaService.GetClientLocationDataByIDAsync(user.DistrictIds, user.CustomerAccounts, user.Roles));
                return Ok(await _anteaService.GetClientLocationDataAsync());
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("CorrosionAnalysis")]
        public async Task<IActionResult> GetCorrosionAnalysis(string operationId)
        {
            try
            {
                return Ok(await _anteaService.GetCorrosionAnalysisAsync(operationId));
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("GeneralAnalysis")]
        public async Task<IActionResult> GetGeneralAnalysis([FromQuery] string assetId)
        {
            try
            {

                return Ok(await _anteaService.GetGeneralAnalysis(assetId));
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("EquipmentData")]
        public async Task<IActionResult> GetEquipments()
        {
            try
            {

                return Ok(_anteaService.GetEquipmentData());
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("Inspections")]
        public async Task<IActionResult> GetInspections()
        {
            try
            {
                var inspections = await _anteaService.GetInspectionsAsync();
                var filteredInspections = await FilterByUserRoleAsync(inspections, i => i.LOCATIONID);
                return Ok(filteredInspections);
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("InspectionsAttachments")]
        public async Task<IActionResult> GetInspectionAttachments(string operationid)
        {
            try
            {
                var inspectionAttachements = await _anteaService.GetInspectionAttachmentsAsync(operationid);
                return Ok(inspectionAttachements);
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("Anomalies")]
        public async Task<IActionResult> GetAnamolies()
        {
            try
            {
                var anomalies = await _anteaService.GetAnomaliesAsync();
                var filteredAnomalies = await FilterByUserRoleAsync(anomalies, a => a.LOCATIONID);
                return Ok(filteredAnomalies);
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }

        [HttpPost("UploadFilesToGCPBucket")]
        public async Task<IActionResult> UploadFilesToGCPBucket([FromBody] SubmissionFileUpload submissionFile)
        {

            if (await _anteaService.UploadSubmissionFilesToGCPBucket(submissionFile))
                return Ok("FileUploaded successfully");
            else
                return BadRequest("Error while uploading file");
        }
        [HttpPost("Submissions")]
        public async Task<IActionResult> PostSubmissions([FromBody] Submissions submissions)
        {
            _logger.LogInformation("Received submission request for asset submission.");
            try
            {
                // Migrated from Firestore to Azure Cosmos DB
                Guid Id = Guid.NewGuid();
                submissions.Id = Id.ToString();
                var _cosmosAssetsInfo = new List<CosmosSubmissionAssetInfo>();
                var _cosmosAnomalyInfo = new CosmosSubmissionAnomalyInfo();

                if (submissions.AssetInfo != null && submissions.AssetInfo?.Count != 0)
                {
                    foreach (var asset in submissions.AssetInfo)
                    {
                        _cosmosAssetsInfo.Add(new CosmosSubmissionAssetInfo
                        {
                            AssetName = asset?.AssetName,
                            AssetDescription = asset?.AssetDescription,
                            AssetId = asset?.AssetId,
                        });
                    }
                }

                if (submissions.LocationId != null)
                {
                    submissions.CostCenter = await _anteaService.GetCostCenter(submissions.LocationId);
                }

                if (submissions.AnomalyInfo != null)
                {
                    _cosmosAnomalyInfo = new CosmosSubmissionAnomalyInfo
                    {
                        AnomalyId = submissions.AnomalyInfo?.AnomalyNumber,
                        AnomalyNumber = submissions.AnomalyInfo?.AnomalyNumber,
                        AnomalyPriority = submissions.AnomalyInfo?.AnomalyPriority,
                        AnomalyType = submissions.AnomalyInfo?.AnomalyType,
                        AnomalyInspectionOperation = submissions.AnomalyInfo?.AnomalyInspectionOperation
                    };
                }

                var _cosmosSubmissions = new CosmosSubmissions
                {
                    Id = Id.ToString(),
                    ClientFacility = submissions.ClientFacility,
                    SubmissionType = submissions.SubmissionType,
                    AnomalyInfo = _cosmosAnomalyInfo,
                    ClientClosedDate = submissions.ClientClosedDate,
                    Comment = submissions.Comment,
                    AssetInfo = _cosmosAssetsInfo,
                    ServiceType = submissions.ServiceType,
                    CreatedDate = submissions.CreatedDate,
                    SubmittedUser = submissions.SenderInfo.GivenName + " " + submissions.SenderInfo.Surname,
                    CostCenter = submissions.CostCenter
                };

                List<CosmosSubmissionFileUpload> _cosmosDocuments = new List<CosmosSubmissionFileUpload>();
                if (submissions.Documents != null)
                {
                    foreach (var doc in submissions.Documents)
                    {
                        var randomGuid = Guid.NewGuid();
                        var _doc = new SubmissionFileUpload()
                        {
                            FileData = doc.FileData,
                            FileType = doc.FileType,
                            FileName = (randomGuid.ToString() + '_' + doc.FileName)
                        };

                        // Migrated from GCP bucket to Azure Blob Storage
                        if (await _anteaService.UploadSubmissionFilesToGCPBucket(_doc))
                        {
                            _cosmosDocuments.Add(new CosmosSubmissionFileUpload
                            {
                                FileName = _doc.FileName,
                                FileType = _doc.FileType
                            });
                        }
                    }
                    _cosmosSubmissions.Documents = _cosmosDocuments;
                }
                // Migrated from Firestore to Azure Cosmos DB
                await _submissionsContainer.CreateItemAsync(_cosmosSubmissions, new PartitionKey(_cosmosSubmissions.Id));
                _logger.LogInformation("Attempting to send submission email.");
                var emailResult = await _anteaService.SendSubmissionMail(submissions, _cosmosDocuments, _emails);
                if (emailResult == "Success")
                {
                    _logger.LogInformation("Submission email sent successfully.");
                    return Ok(new
                    {
                        message = "Submission submitted successfully",
                        result = "Success"
                    });
                }
                else
                {
                    _logger.LogError($"Submission submitted but email failed to send. Error: {emailResult}");
                    return Ok(new
                    {
                        message = "Submission submitted successfully but there is problem while sending email. Error Message::" + emailResult,
                        result = "Success"
                    });
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occurred during asset submission.");
                return BadRequest("Error:: " + e.Message);
            }

        }

        [HttpGet("Submissions")]
        public async Task<IActionResult> Submissions(string assetid)
        {
            try
            {
                return Ok(await _anteaService.GetSubmissionsAsync(assetid));
            }
            catch (Exception e)
            {
                return BadRequest("Error:: " + e.ToString());
            }
        }
        [HttpDelete("Submissions")]
        public async Task<IActionResult> DeleteSubmission(string id)
        {
            try
            {
                // Validate the id parameter
                if (string.IsNullOrWhiteSpace(id))
                {
                    return BadRequest(new
                    {
                        message = "Error: Submission ID cannot be null, empty, or whitespace.",
                        result = "Failure"
                    });
                }

                // Migrated from Firestore to Azure Cosmos DB
                try
                {
                    await _submissionsContainer.DeleteItemAsync<CosmosSubmissions>(id, new PartitionKey(id));
                    return Ok(new
                    {
                        message = "Submission deleted successfully",
                        result = "Success"
                    });
                }
                catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new
                    {
                        message = "Submission not found",
                        result = "Failure"
                    });
                }
            }
            catch (Exception e)
            {
                return BadRequest(new
                {
                    message = "Error: " + e.Message,
                    result = "Failure"
                });
            }

        }
        [HttpGet("SystemManagementCategories")]
        public async Task<IActionResult> SystemManagementCategories()
        {
            try
            {
                return Ok(await _anteaService.GetSystemManagementCategories());
            }
            catch (Exception e)
            {
                return BadRequest("Error:: " + e.ToString());
            }
        }

        [HttpGet("DiagnoseNextDueDateNotes/{assetId}")]
        public async Task<IActionResult> DiagnoseNextDueDateNotes(string assetId)
        {
            try
            {
                var diagnosticData = await _anteaService.DiagnoseNextDueDateNotes(assetId);
                return Ok(diagnosticData);
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
    }
}
